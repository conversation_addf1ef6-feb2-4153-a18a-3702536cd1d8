import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { motion } from "framer-motion";
import {
  Brain,
  Lightbulb,
  Target,
  TrendingUp,
  RefreshCw,
  Sparkles,
  CheckCircle,
  Clock,
  AlertTriangle
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { BreakAnalytics, generateBreakRecommendations } from '@/utils/breakAnalytics';

interface BreakInsightsProps {
  analytics: BreakAnalytics;
  className?: string;
  userGoals?: {
    targetStudyHours?: number;
    maxBreaksPerDay?: number;
  };
}

const BreakInsights: React.FC<BreakInsightsProps> = ({
  analytics,
  className = "",
  userGoals
}) => {
  const [recommendations, setRecommendations] = useState<{
    immediate: string[];
    longTerm: string[];
    strategies: string[];
  } | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const loadRecommendations = async () => {
    setIsLoading(true);
    try {
      const recs = await generateBreakRecommendations(analytics, userGoals);
      setRecommendations(recs);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error loading recommendations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (analytics.durationStats.breakCount > 0) {
      loadRecommendations();
    }
  }, [analytics]);

  const getInsightIcon = (type: 'immediate' | 'longTerm' | 'strategies') => {
    switch (type) {
      case 'immediate':
        return <Clock className="h-4 w-4 text-orange-500" />;
      case 'longTerm':
        return <Target className="h-4 w-4 text-blue-500" />;
      case 'strategies':
        return <Lightbulb className="h-4 w-4 text-purple-500" />;
    }
  };

  const getInsightColor = (type: 'immediate' | 'longTerm' | 'strategies') => {
    switch (type) {
      case 'immediate':
        return 'border-l-orange-500 bg-orange-50/50 dark:bg-orange-900/10';
      case 'longTerm':
        return 'border-l-blue-500 bg-blue-50/50 dark:bg-blue-900/10';
      case 'strategies':
        return 'border-l-purple-500 bg-purple-50/50 dark:bg-purple-900/10';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className={className}
    >
      <Card className="bg-card/80 backdrop-blur-sm border shadow-lg">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-medium flex items-center gap-2">
              <Brain className="h-5 w-5 text-violet-600" />
              <span>AI-Powered Break Insights</span>
              <Badge variant="secondary" className="ml-2 bg-violet-100 text-violet-700 dark:bg-violet-900/30 dark:text-violet-300">
                <Sparkles className="h-3 w-3 mr-1" />
                Gemini AI
              </Badge>
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={loadRecommendations}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
          {lastUpdated && (
            <p className="text-xs text-muted-foreground">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </p>
          )}
        </CardHeader>
        <CardContent>
          {/* General Insights */}
          {analytics.insights && analytics.insights.length > 0 && (
            <div className="mb-6">
              <h4 className="text-sm font-semibold text-foreground mb-3 flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-emerald-600" />
                Pattern Analysis
              </h4>
              <div className="space-y-2">
                {analytics.insights.slice(0, 3).map((insight, index) => (
                  <Alert key={index} className="border-l-4 border-l-emerald-500 bg-emerald-50/50 dark:bg-emerald-900/10">
                    <CheckCircle className="h-4 w-4 text-emerald-600" />
                    <AlertDescription className="text-sm text-foreground">
                      {insight}
                    </AlertDescription>
                  </Alert>
                ))}
              </div>
            </div>
          )}

          {/* Personalized Recommendations */}
          {recommendations ? (
            <Tabs defaultValue="immediate" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="immediate" className="text-xs px-2 py-1">
                  Quick Wins
                </TabsTrigger>
                <TabsTrigger value="longTerm" className="text-xs px-2 py-1">
                  Long-term
                </TabsTrigger>
                <TabsTrigger value="strategies" className="text-xs px-2 py-1">
                  Strategies
                </TabsTrigger>
              </TabsList>

              <TabsContent value="immediate" className="mt-4">
                <div className="space-y-3">
                  <h4 className="text-sm font-semibold text-foreground flex items-center gap-2">
                    {getInsightIcon('immediate')}
                    Immediate Actions (Next 1-3 days)
                  </h4>
                  {recommendations.immediate.length > 0 ? (
                    recommendations.immediate.map((action, index) => (
                      <Alert key={index} className={`border-l-4 ${getInsightColor('immediate')}`}>
                        <AlertTriangle className="h-4 w-4 text-orange-600" />
                        <AlertDescription className="text-sm text-foreground">
                          {action}
                        </AlertDescription>
                      </Alert>
                    ))
                  ) : (
                    <p className="text-sm text-muted-foreground">No immediate actions needed.</p>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="longTerm" className="mt-4">
                <div className="space-y-3">
                  <h4 className="text-sm font-semibold text-foreground flex items-center gap-2">
                    {getInsightIcon('longTerm')}
                    Long-term Strategies (Next 2-4 weeks)
                  </h4>
                  {recommendations.longTerm.length > 0 ? (
                    recommendations.longTerm.map((strategy, index) => (
                      <Alert key={index} className={`border-l-4 ${getInsightColor('longTerm')}`}>
                        <Target className="h-4 w-4 text-blue-600" />
                        <AlertDescription className="text-sm text-foreground">
                          {strategy}
                        </AlertDescription>
                      </Alert>
                    ))
                  ) : (
                    <p className="text-sm text-muted-foreground">No long-term strategies available.</p>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="strategies" className="mt-4">
                <div className="space-y-3">
                  <h4 className="text-sm font-semibold text-foreground flex items-center gap-2">
                    {getInsightIcon('strategies')}
                    Targeted Techniques
                  </h4>
                  {recommendations.strategies.length > 0 ? (
                    recommendations.strategies.map((technique, index) => (
                      <Alert key={index} className={`border-l-4 ${getInsightColor('strategies')}`}>
                        <Lightbulb className="h-4 w-4 text-purple-600" />
                        <AlertDescription className="text-sm text-foreground">
                          {technique}
                        </AlertDescription>
                      </Alert>
                    ))
                  ) : (
                    <p className="text-sm text-muted-foreground">No specific strategies available.</p>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          ) : isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="flex items-center gap-3">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-violet-600"></div>
                <span className="text-sm text-muted-foreground">Generating personalized insights...</span>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <Brain className="h-12 w-12 text-muted-foreground mx-auto mb-3 opacity-50" />
              <p className="text-sm text-muted-foreground mb-4">
                AI insights will appear here once you have break data
              </p>
              <Button onClick={loadRecommendations} variant="outline" size="sm">
                Generate Insights
              </Button>
            </div>
          )}

          {/* Break Efficiency Score */}
          {analytics.durationStats.breakCount > 0 && (
            <div className="mt-6 p-4 bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-900/20 dark:to-purple-900/20 rounded-lg border border-violet-200/50 dark:border-violet-700/50">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-semibold text-violet-700 dark:text-violet-300">
                    Break Efficiency Score
                  </h4>
                  <p className="text-xs text-violet-600 dark:text-violet-400">
                    Based on frequency and duration patterns
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-violet-600">
                    {calculateEfficiencyScore(analytics)}
                  </div>
                  <div className="text-xs text-violet-500">
                    {getScoreDescription(calculateEfficiencyScore(analytics))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

// Helper functions
const calculateEfficiencyScore = (analytics: BreakAnalytics): string => {
  if (analytics.durationStats.breakCount === 0) return 'N/A';
  
  const avgFrequency = analytics.frequencyData.length > 0 
    ? analytics.frequencyData.reduce((sum, day) => sum + day.breakFrequencyRate, 0) / analytics.frequencyData.length 
    : 0;
  
  if (avgFrequency <= 1) return 'A+';
  if (avgFrequency <= 2) return 'A';
  if (avgFrequency <= 3) return 'B';
  if (avgFrequency <= 4) return 'C';
  return 'D';
};

const getScoreDescription = (score: string): string => {
  switch (score) {
    case 'A+': return 'Excellent focus';
    case 'A': return 'Very good';
    case 'B': return 'Good';
    case 'C': return 'Needs improvement';
    case 'D': return 'Frequent breaks';
    default: return 'No data';
  }
};

export default BreakInsights;
