import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { motion } from "framer-motion";
import { Coffee, Clock, TrendingUp, AlertTriangle, CheckCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { getBreakAnalyticsForDate, processBreakAnalytics, BreakAnalytics } from '@/utils/breakAnalytics';

interface BreakOverviewProps {
  studySessions: any[];
  selectedDate: string;
  formatDuration: (seconds: number) => string;
  className?: string;
}

const BreakOverview: React.FC<BreakOverviewProps> = ({
  studySessions,
  selectedDate,
  formatDuration,
  className = ""
}) => {
  const [breakAnalytics, setBreakAnalytics] = useState<BreakAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadBreakAnalytics = async () => {
      setIsLoading(true);
      try {
        const analytics = await processBreakAnalytics(studySessions);
        setBreakAnalytics(analytics);
      } catch (error) {
        console.error('Error processing break analytics:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadBreakAnalytics();
  }, [studySessions]);

  if (isLoading) {
    return (
      <Card className={`bg-card/80 backdrop-blur-sm border shadow-lg ${className}`}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-24">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-violet-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!breakAnalytics) {
    return (
      <Card className={`bg-card/80 backdrop-blur-sm border shadow-lg ${className}`}>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg font-medium flex items-center gap-2">
            <Coffee className="h-5 w-5 text-violet-600" />
            <span>Break Analysis</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-sm">No break data available</p>
        </CardContent>
      </Card>
    );
  }

  const dayBreakData = getBreakAnalyticsForDate(breakAnalytics, selectedDate);
  const topReason = breakAnalytics.reasonAnalysis[0];
  
  // Calculate break efficiency score (lower is better)
  const getBreakEfficiencyScore = () => {
    if (dayBreakData.totalStudyTime === 0) return 'N/A';
    
    const breakRatio = dayBreakData.breakCount / (dayBreakData.totalStudyTime / 3600); // breaks per hour
    if (breakRatio <= 1) return 'Excellent';
    if (breakRatio <= 2) return 'Good';
    if (breakRatio <= 3) return 'Fair';
    return 'Needs Improvement';
  };

  const efficiencyScore = getBreakEfficiencyScore();
  const getScoreColor = (score: string) => {
    switch (score) {
      case 'Excellent': return 'text-green-600';
      case 'Good': return 'text-emerald-600';
      case 'Fair': return 'text-yellow-600';
      case 'Needs Improvement': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getScoreIcon = (score: string) => {
    switch (score) {
      case 'Excellent':
      case 'Good':
        return <CheckCircle className="h-4 w-4" />;
      case 'Fair':
        return <Clock className="h-4 w-4" />;
      case 'Needs Improvement':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <Coffee className="h-4 w-4" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className={className}
    >
      <Card className="bg-card/80 backdrop-blur-sm border shadow-lg h-full">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-medium flex items-center gap-2">
            <Coffee className="h-5 w-5 text-violet-600" />
            <span>Today's Break Analysis</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Daily Break Stats */}
          <div className="grid grid-cols-2 gap-3 sm:gap-4">
            <div className="text-center p-2 sm:p-3 bg-violet-50/50 dark:bg-violet-900/20 rounded-lg border border-violet-200/50 dark:border-violet-700/50">
              <p className="text-xl sm:text-2xl font-bold text-violet-600">{dayBreakData.breakCount}</p>
              <p className="text-xs text-muted-foreground">Breaks Taken</p>
            </div>
            <div className="text-center p-2 sm:p-3 bg-emerald-50/50 dark:bg-emerald-900/20 rounded-lg border border-emerald-200/50 dark:border-emerald-700/50">
              <p className="text-xl sm:text-2xl font-bold text-emerald-600">
                {dayBreakData.averageBreakDuration > 0
                  ? formatDuration(dayBreakData.averageBreakDuration).split(' ')[0]
                  : '0'
                }
              </p>
              <p className="text-xs text-muted-foreground">Avg Duration</p>
            </div>
          </div>

          {/* Break Efficiency Score */}
          <div className="p-3 bg-muted/40 rounded-lg border">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className={getScoreColor(efficiencyScore)}>
                  {getScoreIcon(efficiencyScore)}
                </span>
                <span className="text-sm font-medium">Break Efficiency</span>
              </div>
              <Badge 
                variant="outline" 
                className={`${getScoreColor(efficiencyScore)} border-current`}
              >
                {efficiencyScore}
              </Badge>
            </div>
            {dayBreakData.breakFrequencyRate > 0 && (
              <p className="text-xs text-muted-foreground mt-1">
                {dayBreakData.breakFrequencyRate.toFixed(1)} breaks per hour
              </p>
            )}
          </div>

          {/* Top Break Reason */}
          {topReason && (
            <div className="p-3 bg-muted/40 rounded-lg border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">Most Common Reason</p>
                  <p className="text-xs text-muted-foreground">{topReason.reason}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-bold text-rose-600">{topReason.count}</p>
                  <p className="text-xs text-muted-foreground">times</p>
                </div>
              </div>
              <div className="flex items-center gap-1 mt-2">
                {topReason.trend === 'increasing' && (
                  <>
                    <TrendingUp className="h-3 w-3 text-red-500" />
                    <span className="text-xs text-red-500">Increasing</span>
                  </>
                )}
                {topReason.trend === 'decreasing' && (
                  <>
                    <TrendingUp className="h-3 w-3 text-green-500 rotate-180" />
                    <span className="text-xs text-green-500">Decreasing</span>
                  </>
                )}
                {topReason.trend === 'stable' && (
                  <span className="text-xs text-gray-500">Stable pattern</span>
                )}
              </div>
            </div>
          )}

          {/* Quick Insight */}
          {breakAnalytics.insights && breakAnalytics.insights.length > 0 && (
            <div className="p-3 bg-blue-50/50 dark:bg-blue-900/20 rounded-lg border border-blue-200/50 dark:border-blue-700/50">
              <p className="text-xs text-blue-700 dark:text-blue-300 font-medium mb-1">
                💡 Quick Insight
              </p>
              <p className="text-xs text-blue-600 dark:text-blue-400">
                {breakAnalytics.insights[0]}
              </p>
            </div>
          )}

          {/* No breaks message */}
          {dayBreakData.breakCount === 0 && dayBreakData.totalStudyTime > 0 && (
            <div className="text-center py-4">
              <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
              <p className="text-sm font-medium text-green-600">Great focus today!</p>
              <p className="text-xs text-muted-foreground">No breaks recorded</p>
            </div>
          )}

          {/* No study data message */}
          {dayBreakData.totalStudyTime === 0 && (
            <div className="text-center py-4">
              <Coffee className="h-8 w-8 text-muted-foreground mx-auto mb-2 opacity-50" />
              <p className="text-sm text-muted-foreground">No study data for today</p>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default BreakOverview;
