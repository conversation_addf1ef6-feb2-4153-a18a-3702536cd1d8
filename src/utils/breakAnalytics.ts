import { getGeminiModelInstance } from './apiUtils';

// Types for break analysis
export interface BreakRecord {
  id: string;
  userId: string;
  subject: string;
  taskName: string;
  taskType: string;
  startTime: Date;
  endTime: Date;
  duration: number; // in seconds
  reason: string; // from notes/feedback
  date: string;
  productivityRating?: number;
}

export interface BreakFrequencyData {
  date: string;
  breakCount: number;
  totalStudyTime: number; // in seconds
  averageBreakDuration: number; // in seconds
  breakFrequencyRate: number; // breaks per hour of study
}

export interface BreakDurationStats {
  averageDuration: number; // in seconds
  shortestBreak: number; // in seconds
  longestBreak: number; // in seconds
  medianDuration: number; // in seconds
  totalBreakTime: number; // in seconds
  breakCount: number;
}

export interface BreakReasonAnalysis {
  reason: string;
  count: number;
  totalDuration: number; // in seconds
  averageDuration: number; // in seconds
  percentage: number;
  trend: 'increasing' | 'decreasing' | 'stable';
}

export interface BreakAnalytics {
  frequencyData: BreakFrequencyData[];
  durationStats: BreakDurationStats;
  reasonAnalysis: BreakReasonAnalysis[];
  weeklyTrends: {
    week: string;
    averageBreaksPerDay: number;
    averageBreakDuration: number;
    totalBreaks: number;
  }[];
  dailyPatterns: {
    hour: number;
    breakCount: number;
    averageDuration: number;
  }[];
  insights?: string[];
}

// Extract break records from study sessions
export const extractBreakRecords = (studySessions: any[]): BreakRecord[] => {
  return studySessions
    .filter(session => session.phase === 'pause' && !session.completed)
    .map(session => ({
      id: session.id,
      userId: session.user_id,
      subject: session.subject || 'Unknown',
      taskName: session.task_name || 'Study Session',
      taskType: session.task_type || 'Study',
      startTime: new Date(session.start_time),
      endTime: session.end_time ? new Date(session.end_time) : new Date(session.start_time),
      duration: session.duration || 0,
      reason: session.notes || session.feedback || 'No reason provided',
      date: session.date,
      productivityRating: session.productivity_rating
    }));
};

// Calculate break frequency data
export const calculateBreakFrequency = (
  breakRecords: BreakRecord[],
  studySessions: any[]
): BreakFrequencyData[] => {
  const dateMap = new Map<string, {
    breaks: BreakRecord[];
    studyTime: number;
  }>();

  // Group breaks by date
  breakRecords.forEach(breakRecord => {
    if (!dateMap.has(breakRecord.date)) {
      dateMap.set(breakRecord.date, { breaks: [], studyTime: 0 });
    }
    dateMap.get(breakRecord.date)!.breaks.push(breakRecord);
  });

  // Calculate study time for each date
  studySessions
    .filter(session => session.phase !== 'pause' && session.completed)
    .forEach(session => {
      if (!dateMap.has(session.date)) {
        dateMap.set(session.date, { breaks: [], studyTime: 0 });
      }
      dateMap.get(session.date)!.studyTime += session.duration || 0;
    });

  // Convert to frequency data
  return Array.from(dateMap.entries()).map(([date, data]) => {
    const breakCount = data.breaks.length;
    const totalStudyTime = data.studyTime;
    const averageBreakDuration = breakCount > 0 
      ? data.breaks.reduce((sum, br) => sum + br.duration, 0) / breakCount 
      : 0;
    const breakFrequencyRate = totalStudyTime > 0 
      ? (breakCount / (totalStudyTime / 3600)) // breaks per hour
      : 0;

    return {
      date,
      breakCount,
      totalStudyTime,
      averageBreakDuration,
      breakFrequencyRate
    };
  }).sort((a, b) => a.date.localeCompare(b.date));
};

// Calculate break duration statistics
export const calculateBreakDurationStats = (breakRecords: BreakRecord[]): BreakDurationStats => {
  if (breakRecords.length === 0) {
    return {
      averageDuration: 0,
      shortestBreak: 0,
      longestBreak: 0,
      medianDuration: 0,
      totalBreakTime: 0,
      breakCount: 0
    };
  }

  const durations = breakRecords.map(br => br.duration).sort((a, b) => a - b);
  const totalBreakTime = durations.reduce((sum, duration) => sum + duration, 0);
  const averageDuration = totalBreakTime / durations.length;
  const medianDuration = durations.length % 2 === 0
    ? (durations[durations.length / 2 - 1] + durations[durations.length / 2]) / 2
    : durations[Math.floor(durations.length / 2)];

  return {
    averageDuration,
    shortestBreak: durations[0],
    longestBreak: durations[durations.length - 1],
    medianDuration,
    totalBreakTime,
    breakCount: durations.length
  };
};

// Categorize and analyze break reasons
export const analyzeBreakReasons = (breakRecords: BreakRecord[]): BreakReasonAnalysis[] => {
  const reasonMap = new Map<string, {
    count: number;
    totalDuration: number;
    recentBreaks: BreakRecord[];
  }>();

  // Categorize reasons using keywords
  const categorizeReason = (reason: string): string => {
    const lowerReason = reason.toLowerCase();

    if (lowerReason.includes('tired') || lowerReason.includes('fatigue') || lowerReason.includes('exhausted')) {
      return 'Fatigue/Tiredness';
    } else if (lowerReason.includes('distract') || lowerReason.includes('focus') || lowerReason.includes('concentration')) {
      return 'Distraction/Focus Issues';
    } else if (lowerReason.includes('bathroom') || lowerReason.includes('restroom') || lowerReason.includes('toilet')) {
      return 'Bathroom Break';
    } else if (lowerReason.includes('water') || lowerReason.includes('drink') || lowerReason.includes('thirsty')) {
      return 'Hydration Break';
    } else if (lowerReason.includes('food') || lowerReason.includes('snack') || lowerReason.includes('hungry') || lowerReason.includes('eat')) {
      return 'Food/Snack Break';
    } else if (lowerReason.includes('phone') || lowerReason.includes('message') || lowerReason.includes('notification') || lowerReason.includes('social')) {
      return 'Phone/Social Media';
    } else if (lowerReason.includes('stretch') || lowerReason.includes('exercise') || lowerReason.includes('walk') || lowerReason.includes('movement')) {
      return 'Physical Activity';
    } else if (lowerReason.includes('call') || lowerReason.includes('family') || lowerReason.includes('friend') || lowerReason.includes('talk')) {
      return 'Social Interaction';
    } else if (lowerReason.includes('difficult') || lowerReason.includes('hard') || lowerReason.includes('stuck') || lowerReason.includes('confused')) {
      return 'Difficulty with Material';
    } else if (lowerReason.includes('bored') || lowerReason.includes('boring') || lowerReason.includes('monotonous')) {
      return 'Boredom';
    } else if (lowerReason === 'no reason provided' || lowerReason.trim() === '') {
      return 'No Reason Provided';
    } else {
      return 'Other';
    }
  };

  // Group breaks by categorized reason
  breakRecords.forEach(breakRecord => {
    const category = categorizeReason(breakRecord.reason);

    if (!reasonMap.has(category)) {
      reasonMap.set(category, { count: 0, totalDuration: 0, recentBreaks: [] });
    }

    const data = reasonMap.get(category)!;
    data.count++;
    data.totalDuration += breakRecord.duration;
    data.recentBreaks.push(breakRecord);
  });

  const totalBreaks = breakRecords.length;

  // Calculate trends (simplified - comparing recent vs older breaks)
  const calculateTrend = (recentBreaks: BreakRecord[]): 'increasing' | 'decreasing' | 'stable' => {
    if (recentBreaks.length < 4) return 'stable';

    const sortedBreaks = recentBreaks.sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
    const midPoint = Math.floor(sortedBreaks.length / 2);
    const olderHalf = sortedBreaks.slice(0, midPoint);
    const recentHalf = sortedBreaks.slice(midPoint);

    const olderAvg = olderHalf.length / (olderHalf.length > 0 ? 1 : 1);
    const recentAvg = recentHalf.length / (recentHalf.length > 0 ? 1 : 1);

    if (recentAvg > olderAvg * 1.2) return 'increasing';
    if (recentAvg < olderAvg * 0.8) return 'decreasing';
    return 'stable';
  };

  return Array.from(reasonMap.entries())
    .map(([reason, data]) => ({
      reason,
      count: data.count,
      totalDuration: data.totalDuration,
      averageDuration: data.totalDuration / data.count,
      percentage: totalBreaks > 0 ? (data.count / totalBreaks) * 100 : 0,
      trend: calculateTrend(data.recentBreaks)
    }))
    .sort((a, b) => b.count - a.count);
};

// Calculate weekly trends
export const calculateWeeklyTrends = (breakRecords: BreakRecord[]): {
  week: string;
  averageBreaksPerDay: number;
  averageBreakDuration: number;
  totalBreaks: number;
}[] => {
  const weekMap = new Map<string, BreakRecord[]>();

  breakRecords.forEach(breakRecord => {
    const date = new Date(breakRecord.startTime);
    const weekStart = new Date(date);
    weekStart.setDate(date.getDate() - date.getDay()); // Start of week (Sunday)
    const weekKey = weekStart.toISOString().split('T')[0];

    if (!weekMap.has(weekKey)) {
      weekMap.set(weekKey, []);
    }
    weekMap.get(weekKey)!.push(breakRecord);
  });

  return Array.from(weekMap.entries())
    .map(([week, breaks]) => {
      const totalBreaks = breaks.length;
      const totalDuration = breaks.reduce((sum, br) => sum + br.duration, 0);
      const averageBreakDuration = totalBreaks > 0 ? totalDuration / totalBreaks : 0;
      const averageBreaksPerDay = totalBreaks / 7; // Assuming full week

      return {
        week,
        averageBreaksPerDay,
        averageBreakDuration,
        totalBreaks
      };
    })
    .sort((a, b) => a.week.localeCompare(b.week));
};

// Calculate daily patterns (hourly breakdown)
export const calculateDailyPatterns = (breakRecords: BreakRecord[]): {
  hour: number;
  breakCount: number;
  averageDuration: number;
}[] => {
  const hourMap = new Map<number, { count: number; totalDuration: number }>();

  // Initialize all hours
  for (let hour = 0; hour < 24; hour++) {
    hourMap.set(hour, { count: 0, totalDuration: 0 });
  }

  breakRecords.forEach(breakRecord => {
    const hour = breakRecord.startTime.getHours();
    const data = hourMap.get(hour)!;
    data.count++;
    data.totalDuration += breakRecord.duration;
  });

  return Array.from(hourMap.entries())
    .map(([hour, data]) => ({
      hour,
      breakCount: data.count,
      averageDuration: data.count > 0 ? data.totalDuration / data.count : 0
    }))
    .filter(item => item.breakCount > 0); // Only include hours with breaks
};

// Generate AI insights using Gemini
export const generateBreakInsights = async (analytics: BreakAnalytics): Promise<string[]> => {
  try {
    const geminiModel = getGeminiModelInstance();
    if (!geminiModel) {
      return ['AI insights are currently unavailable. Please check your API configuration.'];
    }

    // Calculate additional metrics for better analysis
    const avgBreaksPerDay = analytics.frequencyData.length > 0
      ? analytics.frequencyData.reduce((sum, day) => sum + day.breakCount, 0) / analytics.frequencyData.length
      : 0;

    const recentTrend = analytics.frequencyData.length >= 7
      ? analytics.frequencyData.slice(-3).reduce((sum, day) => sum + day.breakCount, 0) / 3 -
        analytics.frequencyData.slice(-7, -4).reduce((sum, day) => sum + day.breakCount, 0) / 3
      : 0;

    const peakBreakHours = analytics.dailyPatterns
      .sort((a, b) => b.breakCount - a.breakCount)
      .slice(0, 3)
      .map(p => `${p.hour}:00`);

    const prompt = `
As a productivity expert and study coach, analyze this student's break patterns and provide personalized insights:

BREAK STATISTICS:
- Total breaks recorded: ${analytics.durationStats.breakCount}
- Average break duration: ${Math.round(analytics.durationStats.averageDuration / 60)} minutes
- Longest break: ${Math.round(analytics.durationStats.longestBreak / 60)} minutes
- Shortest break: ${Math.round(analytics.durationStats.shortestBreak / 60)} minutes
- Total break time: ${Math.round(analytics.durationStats.totalBreakTime / 3600)} hours
- Average breaks per day: ${avgBreaksPerDay.toFixed(1)}

BREAK REASONS ANALYSIS:
${analytics.reasonAnalysis.slice(0, 6).map(reason =>
  `- ${reason.reason}: ${reason.count} times (${reason.percentage.toFixed(1)}%) - Trend: ${reason.trend}`
).join('\n')}

TIMING PATTERNS:
- Peak break hours: ${peakBreakHours.join(', ')}
- Recent trend: ${recentTrend > 0 ? `+${recentTrend.toFixed(1)} more breaks/day` : `${recentTrend.toFixed(1)} fewer breaks/day`}

RECENT DAILY PATTERNS:
${analytics.frequencyData.slice(-7).map(day =>
  `- ${day.date}: ${day.breakCount} breaks (${day.breakFrequencyRate.toFixed(1)} per hour)`
).join('\n')}

Provide 4-6 specific, actionable insights focusing on:
1. Identifying the most concerning patterns that hurt productivity
2. Recognizing positive habits to reinforce
3. Suggesting specific strategies for the top break reasons
4. Recommending optimal break timing based on their patterns
5. Providing concrete next steps for improvement

Format each insight as a clear, actionable recommendation. Be encouraging but honest about areas needing improvement.
`;

    const result = await geminiModel.generateContent(prompt);
    const response = result.response.text();

    // Enhanced parsing to extract insights
    const insights = response
      .split(/\n+/)
      .filter(line => {
        const trimmed = line.trim();
        return trimmed.length > 20 &&
               (trimmed.match(/^[\d•\-*]./) ||
                trimmed.includes('recommend') ||
                trimmed.includes('suggest') ||
                trimmed.includes('consider') ||
                trimmed.includes('try') ||
                trimmed.includes('focus on'));
      })
      .map(line => line.replace(/^[\d•\-*.\s]+/, '').trim())
      .filter(insight => insight.length > 15 && insight.length < 200)
      .slice(0, 6);

    return insights.length > 0 ? insights : [
      'Your break patterns show room for optimization. Consider tracking break reasons more consistently.',
      'Try implementing the Pomodoro Technique with structured 5-minute breaks every 25 minutes.',
      'Focus on identifying your peak distraction times and plan accordingly.'
    ];
  } catch (error) {
    console.error('Error generating break insights:', error);
    return ['AI insights are currently unavailable. Please try again later.'];
  }
};

// Generate personalized break recommendations
export const generateBreakRecommendations = async (
  analytics: BreakAnalytics,
  userGoals?: { targetStudyHours?: number; maxBreaksPerDay?: number }
): Promise<{
  immediate: string[];
  longTerm: string[];
  strategies: string[];
}> => {
  try {
    const geminiModel = getGeminiModelInstance();
    if (!geminiModel) {
      return {
        immediate: ['AI recommendations are currently unavailable.'],
        longTerm: ['Please check your API configuration.'],
        strategies: ['Try manual break optimization techniques.']
      };
    }

    const topReasons = analytics.reasonAnalysis.slice(0, 3);
    const avgBreakDuration = Math.round(analytics.durationStats.averageDuration / 60);

    const prompt = `
As a study productivity coach, create a personalized break optimization plan for this student:

CURRENT SITUATION:
- Taking ${analytics.durationStats.breakCount} breaks total
- Average break: ${avgBreakDuration} minutes
- Top break reasons: ${topReasons.map(r => `${r.reason} (${r.percentage.toFixed(1)}%)`).join(', ')}
- ${userGoals?.targetStudyHours ? `Target study hours: ${userGoals.targetStudyHours}/day` : 'No specific study goals set'}

Create three categories of recommendations:

IMMEDIATE ACTIONS (next 1-3 days):
- 2-3 specific actions they can implement today
- Focus on quick wins and habit adjustments

LONG-TERM STRATEGIES (next 2-4 weeks):
- 2-3 sustainable changes for lasting improvement
- Address root causes of frequent breaks

SPECIFIC STRATEGIES:
- 3-4 targeted techniques for their top break reasons
- Include both prevention and management approaches

Keep recommendations practical, specific, and achievable. Consider their current patterns when suggesting changes.
`;

    const result = await geminiModel.generateContent(prompt);
    const response = result.response.text();

    // Parse the structured response
    const sections = response.split(/(?:IMMEDIATE|LONG-TERM|SPECIFIC)/i);

    const parseSection = (text: string): string[] => {
      return text
        .split('\n')
        .filter(line => line.trim().length > 10 && (line.includes('-') || line.includes('•')))
        .map(line => line.replace(/^[-•\s]+/, '').trim())
        .filter(item => item.length > 15)
        .slice(0, 4);
    };

    return {
      immediate: parseSection(sections[1] || ''),
      longTerm: parseSection(sections[2] || ''),
      strategies: parseSection(sections[3] || '')
    };
  } catch (error) {
    console.error('Error generating break recommendations:', error);
    return {
      immediate: ['Set a timer for 25-minute focused work sessions'],
      longTerm: ['Develop consistent break scheduling habits'],
      strategies: ['Use the 20-20-20 rule for eye strain breaks']
    };
  }
};

// Main function to process all break analytics
export const processBreakAnalytics = async (studySessions: any[]): Promise<BreakAnalytics> => {
  const breakRecords = extractBreakRecords(studySessions);

  const frequencyData = calculateBreakFrequency(breakRecords, studySessions);
  const durationStats = calculateBreakDurationStats(breakRecords);
  const reasonAnalysis = analyzeBreakReasons(breakRecords);
  const weeklyTrends = calculateWeeklyTrends(breakRecords);
  const dailyPatterns = calculateDailyPatterns(breakRecords);

  const analytics: BreakAnalytics = {
    frequencyData,
    durationStats,
    reasonAnalysis,
    weeklyTrends,
    dailyPatterns
  };

  // Generate AI insights
  try {
    const insights = await generateBreakInsights(analytics);
    analytics.insights = insights;
  } catch (error) {
    console.error('Error generating insights:', error);
    analytics.insights = ['AI insights are currently unavailable.'];
  }

  return analytics;
};

// Helper function to get break analytics for a specific date
export const getBreakAnalyticsForDate = (analytics: BreakAnalytics, date: string) => {
  const dayData = analytics.frequencyData.find(day => day.date === date);

  return {
    breakCount: dayData?.breakCount || 0,
    averageBreakDuration: dayData?.averageBreakDuration || 0,
    breakFrequencyRate: dayData?.breakFrequencyRate || 0,
    totalStudyTime: dayData?.totalStudyTime || 0
  };
};
